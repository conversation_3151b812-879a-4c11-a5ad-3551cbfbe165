/**
 * 四路TB6612电机驱动使用示例
 * 
 * 本示例展示如何在不修改底层配置文件的情况下，
 * 使用现有代码框架驱动四路TB6612电机
 */

#include "SysConfig.h"

/**
 * @brief 四路电机初始化示例
 */
void Example_FourChannel_Init(void)
{
    // 初始化四路电机系统
    Motor_Init_FourChannel();
    
    // 此时四路电机已经初始化完成，可以开始控制
}

/**
 * @brief 基础四路电机控制示例
 */
void Example_FourChannel_BasicControl(void)
{
    // 单独控制每个电机
    Motor_SetDuty_FourChannel(0, 50.0f);   // 电机0正转50%
    Motor_SetDuty_FourChannel(1, -30.0f);  // 电机1反转30%
    Motor_SetDuty_FourChannel(2, 40.0f);   // 电机2正转40%
    Motor_SetDuty_FourChannel(3, -60.0f);  // 电机3反转60%
    
    // 延时1秒
    DL_Common_delayCycles(80000000);
    
    // 批量控制所有电机
    Motor_SetAllDuty_FourChannel(25.0f, 25.0f, 25.0f, 25.0f);
    
    // 延时1秒
    DL_Common_delayCycles(80000000);
    
    // 停止所有电机
    Motor_StopAll_FourChannel();
}

/**
 * @brief 四轮全向移动示例
 */
void Example_FourChannel_OmniMovement(void)
{
    // 前进
    Motor_SetAllDuty_FourChannel(50.0f, 50.0f, 50.0f, 50.0f);
    DL_Common_delayCycles(80000000);
    
    // 后退
    Motor_SetAllDuty_FourChannel(-50.0f, -50.0f, -50.0f, -50.0f);
    DL_Common_delayCycles(80000000);
    
    // 左移（假设为麦克纳姆轮配置）
    Motor_SetAllDuty_FourChannel(-50.0f, 50.0f, 50.0f, -50.0f);
    DL_Common_delayCycles(80000000);
    
    // 右移
    Motor_SetAllDuty_FourChannel(50.0f, -50.0f, -50.0f, 50.0f);
    DL_Common_delayCycles(80000000);
    
    // 顺时针旋转
    Motor_SetAllDuty_FourChannel(50.0f, -50.0f, 50.0f, -50.0f);
    DL_Common_delayCycles(80000000);
    
    // 逆时针旋转
    Motor_SetAllDuty_FourChannel(-50.0f, 50.0f, -50.0f, 50.0f);
    DL_Common_delayCycles(80000000);
    
    // 停止
    Motor_StopAll_FourChannel();
}

/**
 * @brief 兼容性测试示例
 * 验证原有双电机功能是否正常
 */
void Example_Compatibility_Test(void)
{
    // 使用原有的双电机接口
    Motor_Start();
    
    // 差速转向控制
    Motor_SetDuty(&Motor_Left, 50.0f);
    Motor_SetDuty(&Motor_Right, 30.0f);
    
    DL_Common_delayCycles(80000000);
    
    // 停止
    Motor_SetDuty(&Motor_Left, 0.0f);
    Motor_SetDuty(&Motor_Right, 0.0f);
}

/**
 * @brief 主函数示例
 */
void Example_Main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 测试兼容性
    Example_Compatibility_Test();
    
    // 初始化四路电机
    Example_FourChannel_Init();
    
    // 基础控制测试
    Example_FourChannel_BasicControl();
    
    // 全向移动测试
    Example_FourChannel_OmniMovement();
    
    // 进入主循环
    while(1)
    {
        // 可以在这里调用测试函数
        Task_Motor_FourChannel_Test();
        
        // 延时1ms
        DL_Common_delayCycles(80000);
    }
}

/**
 * 引脚分配说明：
 * 
 * PWM信号：
 * - 电机0: PA12 (TIMG0_CC0)
 * - 电机1: PA13 (TIMG0_CC1) 
 * - 电机2: PA14 (TIMG1_CC0)
 * - 电机3: PA15 (TIMG1_CC1)
 * 
 * 方向控制：
 * - 电机0: PB6(AIN1), PB7(AIN2)
 * - 电机1: PB25(BIN1), PB24(BIN2)
 * - 电机2: PB8(CIN1), PB9(CIN2)
 * - 电机3: PB15(DIN1), PB16(DIN2)
 * 
 * 注意事项：
 * 1. 确保硬件连接正确
 * 2. 电机2和电机3需要TIMG1定时器支持
 * 3. 所有GPIO引脚需要正确配置为输出模式
 * 4. 保持与原有双电机代码的兼容性
 */
