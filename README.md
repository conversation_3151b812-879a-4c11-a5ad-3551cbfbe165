## Example Summary

TI MSP430 MSPM0G3507 智能小车项目，支持TB6612四路电机驱动模块。
本项目基于DriverLib开发，集成了电机控制、PID调速、循迹传感器、MPU6050姿态传感器、OLED显示等功能。

## 功能特性

- **四路电机驱动**：支持TB6612四路电机驱动模块，可独立控制四个电机
- **兼容性设计**：保持原有双电机差速转向功能，Motor_Left和Motor_Right作为别名
- **PID速度控制**：每个电机独立的PID速度闭环控制
- **循迹功能**：8路灰度传感器循迹，支持PID转向控制
- **姿态检测**：MPU6050六轴传感器，提供俯仰角、横滚角、偏航角数据
- **OLED显示**：实时显示电机状态、传感器数据等信息
- **串口通信**：支持数据输出和波形显示

## TB6612四路电机驱动模块

### 硬件连接

#### PWM信号引脚
| 电机 | PWM定时器 | PWM引脚 | 功能 |
| --- | --- | --- | --- |
| Motor_1 | TIMG0_CC0 | PA12 | 电机1 PWM控制 |
| Motor_2 | TIMG0_CC1 | PA13 | 电机2 PWM控制 |
| Motor_3 | TIMG1_CC0 | PA14 | 电机3 PWM控制 |
| Motor_4 | TIMG1_CC1 | PA15 | 电机4 PWM控制 |

#### 方向控制引脚
| 电机 | IN1引脚 | IN2引脚 | 功能 |
| --- | --- | --- | --- |
| Motor_1 | PB6 (AIN1) | PB7 (AIN2) | 电机1方向控制 |
| Motor_2 | PB25 (BIN1) | PB24 (BIN2) | 电机2方向控制 |
| Motor_3 | PB8 (CIN1) | PB9 (CIN2) | 电机3方向控制 |
| Motor_4 | PB15 (DIN1) | PB16 (DIN2) | 电机4方向控制 |

### 使用方法

#### 基础使用（兼容原有代码）
```c
// 启动电机（启动前两个电机，保持差速转向功能）
Motor_Start();

// 设置电机占空比（-100.0 到 +100.0）
Motor_SetDuty(&Motor_Left, 50.0f);   // 等同于 Motor_SetDuty(&Motor_1, 50.0f)
Motor_SetDuty(&Motor_Right, -30.0f); // 等同于 Motor_SetDuty(&Motor_2, -30.0f)
```

#### 四路电机控制
```c
// 启动所有四路电机
Motor_StartAll();

// 单独控制每个电机
Motor_SetDuty(&Motor_1, 50.0f);
Motor_SetDuty(&Motor_2, 60.0f);
Motor_SetDuty(&Motor_3, 40.0f);
Motor_SetDuty(&Motor_4, 55.0f);

// 批量设置所有电机占空比
Motor_SetAllDuty(50.0f, 60.0f, 40.0f, 55.0f);

// 停止所有电机
Motor_StopAll();
```

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  |  |
| TIMG0 | PA12 | Motor_1 PWM (CC0) |
| TIMG0 | PA13 | Motor_2 PWM (CC1) |
| TIMG1 | PA14 | Motor_3 PWM (CC0) |
| TIMG1 | PA15 | Motor_4 PWM (CC1) |
| GPIOB | PB6 | Motor_1 Direction (AIN1) |
| GPIOB | PB7 | Motor_1 Direction (AIN2) |
| GPIOB | PB25 | Motor_2 Direction (BIN1) |
| GPIOB | PB24 | Motor_2 Direction (BIN2) |
| GPIOB | PB8 | Motor_3 Direction (CIN1) |
| GPIOB | PB9 | Motor_3 Direction (CIN2) |
| GPIOB | PB15 | Motor_4 Direction (DIN1) |
| GPIOB | PB16 | Motor_4 Direction (DIN2) |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## 编译和使用

### 编译步骤
1. 使用Code Composer Studio (CCS)打开项目
2. 确保已安装MSPM0 SDK
3. 编译项目：Project → Build Project
4. 下载到开发板：Run → Debug

### 运行说明
1. 连接TB6612四路电机驱动模块到指定引脚
2. 连接四个电机到TB6612模块
3. 连接电源（建议使用外部电源为电机供电）
4. 运行程序，电机将根据循迹传感器进行差速转向控制

### 注意事项
- 确保电机电源电压与TB6612模块兼容（通常6-12V）
- 检查所有引脚连接是否正确
- Motor_3和Motor_4默认不参与循迹控制，需要用户自定义控制逻辑
- 可通过OLED显示屏查看实时状态信息
- 支持串口输出调试数据，波特率115200

### 兼容性说明
- 本次修改完全兼容原有的双电机差速转向代码
- Motor_Left和Motor_Right仍然可以正常使用
- 新增的Motor_3和Motor_4可用于扩展功能
- 原有的PID控制和循迹功能保持不变
