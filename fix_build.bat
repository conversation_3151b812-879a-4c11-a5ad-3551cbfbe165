@echo off
echo 正在清理项目...
if exist Debug\*.o del /q Debug\*.o
if exist Debug\*.d del /q Debug\*.d
if exist Debug\ti_msp_dl_config.c del /q Debug\ti_msp_dl_config.c
if exist Debug\ti_msp_dl_config.h del /q Debug\ti_msp_dl_config.h

echo 正在重新生成SysConfig文件...
echo 请在CCS中重新生成项目或使用SysConfig工具重新生成配置文件

echo 修复完成！
echo.
echo 解决方案：
echo 1. 已修复引脚冲突问题（PB15重复分配）
echo 2. 将Gray_Address PIN_1从PB15改为PB17
echo 3. 清理了编译缓存文件
echo.
echo 请在CCS中重新编译项目
pause
