#ifndef __Motor_h
#define __Motor_h

#include "SysConfig.h"

/**
 * @brief 电机方向
 * 
 */
typedef enum
{
    DIRC_NONE = 0,
    DIRC_FOWARD, //正转
    DIRC_BACKWARD //反转
} Motor_DIRC_Def_t;

/**
 * @brief 电机编号
 *
 */
typedef struct
{
    GPTIMER_Regs *Motor_PWM_TIMX; //PWM对应的定时器
    __IO int Motor_PWM_CH; //PWM通道
    __IO uint32_t Motor_Turn_Pin; //电机转动IO Pin
    int16_t *Motor_Encoder_Addr; //电机编码值地址
    Motor_DIRC_Def_t Motor_Dirc; //电机当前正转还是反转
    PID_IQ_Def_t Motor_PID_Instance; //PID实例
    __IO uint32_t Motor_IN1_PIN;  // TB6612的IN1引脚
    __IO uint32_t Motor_IN2_PIN;  // TB6612的IN2引脚
    uint8_t Motor_ID; //电机ID (1-4)
} MOTOR_Def_t;

// 四路电机实例
extern MOTOR_Def_t Motor_1; //电机1
extern MOTOR_Def_t Motor_2; //电机2
extern MOTOR_Def_t Motor_3; //电机3
extern MOTOR_Def_t Motor_4; //电机4

// 兼容性别名（保持原有接口）
#define Motor_Left Motor_1  //左轮别名
#define Motor_Right Motor_2 //右轮别名

// 基础函数
void Motor_Start(void);
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value);
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time);

// 四路驱动扩展函数
void Motor_StartAll(void); //启动所有电机
void Motor_SetAllDuty(float motor1, float motor2, float motor3, float motor4); //设置所有电机占空比
void Motor_StopAll(void); //停止所有电机

#endif