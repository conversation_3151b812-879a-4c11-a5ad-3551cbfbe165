#ifndef __Motor_h
#define __Motor_h

#include "SysConfig.h"

/**
 * @brief 电机方向
 * 
 */
typedef enum
{
    DIRC_NONE = 0,
    DIRC_FOWARD, //正转
    DIRC_BACKWARD //反转
} Motor_DIRC_Def_t;

/**
 * @brief 电机编号
 *
 */
typedef struct
{
    GPTIMER_Regs *Motor_PWM_TIMX; //PWM对应的定时器
    __IO int Motor_PWM_CH; //PWM通道
    __IO uint32_t Motor_Turn_Pin; //电机转动IO Pin
    int16_t *Motor_Encoder_Addr; //电机编码值地址
    Motor_DIRC_Def_t Motor_Dirc; //电机当前正转还是反转
    PID_IQ_Def_t Motor_PID_Instance; //PID实例
    __IO uint32_t Motor_IN1_PIN;  // TB6612的IN1引脚
    __IO uint32_t Motor_IN2_PIN;  // TB6612的IN2引脚
} MOTOR_Def_t;

// 原有双路电机实例（保持兼容性）
extern MOTOR_Def_t Motor_Left; //左轮
extern MOTOR_Def_t Motor_Right; //右轮

// 四路电机实例数组
extern MOTOR_Def_t Motor_Array[4]; //四路电机数组

// 基础函数
void Motor_Start(void);
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value);
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time);

// 四路驱动扩展函数
void Motor_Init_FourChannel(void); //初始化四路电机
void Motor_SetDuty_FourChannel(uint8_t motor_id, float value); //设置指定电机占空比(0-3)
void Motor_SetAllDuty_FourChannel(float m0, float m1, float m2, float m3); //设置所有电机占空比
void Motor_Stop_FourChannel(uint8_t motor_id); //停止指定电机
void Motor_StopAll_FourChannel(void); //停止所有电机

#endif