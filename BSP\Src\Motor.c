#include "Motor.h"

// TB6612 方向控制宏定义
#define AIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN1_PIN)
#define AIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN1_PIN)
#define AIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN2_PIN)
#define AIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN2_PIN)

#define BIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN1_PIN)
#define BIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN1_PIN)
#define BIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN2_PIN)
#define BIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN2_PIN)

// 四路驱动扩展宏定义（使用现有GPIO引脚）
#define CIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN1_PIN)
#define CIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN1_PIN)
#define CIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN2_PIN)
#define CIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN2_PIN)

#define DIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN1_PIN)
#define DIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN1_PIN)
#define DIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN2_PIN)
#define DIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN2_PIN)

// 原有双路电机定义（保持兼容性）
MOTOR_Def_t Motor_Left = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[0],
    .Motor_PWM_TIMX = Motor_PWM_INST,
    .Motor_PWM_CH = DL_TIMER_CC_0_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_AIN1_PIN,
    .Motor_IN2_PIN = DIRC_CTRL_AIN2_PIN
};

MOTOR_Def_t Motor_Right = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[1],
    .Motor_PWM_TIMX = Motor_PWM_INST,
    .Motor_PWM_CH = DL_TIMER_CC_1_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_BIN1_PIN,
    .Motor_IN2_PIN = DIRC_CTRL_BIN2_PIN
};

// 四路电机数组（使用现有PWM资源）
MOTOR_Def_t Motor_Array[4] = {
    // Motor 0 - 使用TIMG0 CC0 (PA12)
    {
        .Motor_Dirc = DIRC_FOWARD,
        .Motor_Encoder_Addr = &Data_MotorEncoder[0],
        .Motor_PWM_TIMX = Motor_PWM_INST,
        .Motor_PWM_CH = DL_TIMER_CC_0_INDEX,
        .Motor_IN1_PIN = DIRC_CTRL_AIN1_PIN,
        .Motor_IN2_PIN = DIRC_CTRL_AIN2_PIN
    },
    // Motor 1 - 使用TIMG0 CC1 (PA13)
    {
        .Motor_Dirc = DIRC_FOWARD,
        .Motor_Encoder_Addr = &Data_MotorEncoder[1],
        .Motor_PWM_TIMX = Motor_PWM_INST,
        .Motor_PWM_CH = DL_TIMER_CC_1_INDEX,
        .Motor_IN1_PIN = DIRC_CTRL_BIN1_PIN,
        .Motor_IN2_PIN = DIRC_CTRL_BIN2_PIN
    },
    // Motor 2 - 使用TIMG1 CC0 (需要配置)
    {
        .Motor_Dirc = DIRC_FOWARD,
        .Motor_Encoder_Addr = &Data_MotorEncoder[2],
        .Motor_PWM_TIMX = Motor_PWM2_INST,
        .Motor_PWM_CH = DL_TIMER_CC_0_INDEX,
        .Motor_IN1_PIN = DIRC_CTRL_CIN1_PIN,
        .Motor_IN2_PIN = DIRC_CTRL_CIN2_PIN
    },
    // Motor 3 - 使用TIMG1 CC1 (需要配置)
    {
        .Motor_Dirc = DIRC_FOWARD,
        .Motor_Encoder_Addr = &Data_MotorEncoder[3],
        .Motor_PWM_TIMX = Motor_PWM2_INST,
        .Motor_PWM_CH = DL_TIMER_CC_1_INDEX,
        .Motor_IN1_PIN = DIRC_CTRL_DIN1_PIN,
        .Motor_IN2_PIN = DIRC_CTRL_DIN2_PIN
    }
};

/**
 * @brief 开启电机（原有函数，保持兼容性）
 */
void Motor_Start(void)
{
    // 开启左右电机PWM定时器
    DL_TimerG_startCounter(Motor_PWM_INST);

    // 初始占空比设为0
    Motor_SetDuty(&Motor_Left, 0.0f);
    Motor_SetDuty(&Motor_Right, 0.0f);

    // 初始化PID
    PID_IQ_Init(&Motor_Left.Motor_PID_Instance);
    PID_IQ_Init(&Motor_Right.Motor_PID_Instance);

    // 设置PID参数
    PID_IQ_SetParams(&Motor_Left.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_Right.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
}

/**
 * @brief 初始化四路电机
 */
void Motor_Init_FourChannel(void)
{
    // 启动两个PWM定时器
    DL_TimerG_startCounter(Motor_PWM_INST);   // TIMG0
    DL_TimerG_startCounter(Motor_PWM2_INST);  // TIMG1

    // 初始化所有电机PID
    for(uint8_t i = 0; i < 4; i++)
    {
        Motor_Array[i].Motor_Dirc = DIRC_FOWARD;
        PID_IQ_Init(&Motor_Array[i].Motor_PID_Instance);
        PID_IQ_SetParams(&Motor_Array[i].Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
        Motor_SetDuty_FourChannel(i, 0.0f); // 初始占空比为0
    }
}

/**
 * @brief 设置电机正反转（适配TB6612）
 */
static bool Motor_SetDirc(MOTOR_Def_t *Motor, Motor_DIRC_Def_t Dirc)
{
    if (Motor == NULL) return false;

    if (Dirc == DIRC_FOWARD)
    {
        // 左电机正转：AIN1=H, AIN2=L
        if (Motor == &Motor_Left)
        {
            AIN1_SET();
            AIN2_CLR();
        }
        // 右电机正转：BIN1=H, BIN2=L
        else if (Motor == &Motor_Right)
        {
            BIN1_SET();
            BIN2_CLR();
        }
        Motor->Motor_Dirc = DIRC_FOWARD;
        return true;
    }
    else if (Dirc == DIRC_BACKWARD)
    {
        // 左电机反转：AIN1=L, AIN2=H
        if (Motor == &Motor_Left)
        {
            AIN1_CLR();
            AIN2_SET();
        }
        // 右电机反转：BIN1=L, BIN2=H
        else if (Motor == &Motor_Right)
        {
            BIN1_CLR();
            BIN2_SET();
        }
        Motor->Motor_Dirc = DIRC_BACKWARD;
        return true;
    }
    return false;
}

/**
 * @brief 设置四路电机方向（内部函数）
 */
static bool Motor_SetDirc_FourChannel(uint8_t motor_id, Motor_DIRC_Def_t Dirc)
{
    if (motor_id >= 4) return false;

    if (Dirc == DIRC_FOWARD)
    {
        switch (motor_id)
        {
            case 0: // 电机0：AIN1=H, AIN2=L
                AIN1_SET();
                AIN2_CLR();
                break;
            case 1: // 电机1：BIN1=H, BIN2=L
                BIN1_SET();
                BIN2_CLR();
                break;
            case 2: // 电机2：CIN1=H, CIN2=L
                CIN1_SET();
                CIN2_CLR();
                break;
            case 3: // 电机3：DIN1=H, DIN2=L
                DIN1_SET();
                DIN2_CLR();
                break;
        }
        Motor_Array[motor_id].Motor_Dirc = DIRC_FOWARD;
        return true;
    }
    else if (Dirc == DIRC_BACKWARD)
    {
        switch (motor_id)
        {
            case 0: // 电机0：AIN1=L, AIN2=H
                AIN1_CLR();
                AIN2_SET();
                break;
            case 1: // 电机1：BIN1=L, BIN2=H
                BIN1_CLR();
                BIN2_SET();
                break;
            case 2: // 电机2：CIN1=L, CIN2=H
                CIN1_CLR();
                CIN2_SET();
                break;
            case 3: // 电机3：DIN1=L, DIN2=H
                DIN1_CLR();
                DIN2_SET();
                break;
        }
        Motor_Array[motor_id].Motor_Dirc = DIRC_BACKWARD;
        return true;
    }
    return false;
}

/**
 * @brief 设置电机占空比（-100~100）
 */
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value)
{
    if (Motor == NULL) return false;

    // 限制占空比范围
    if (value > 100.0f) value = 100.0f;
    if (value < -100.0f) value = -100.0f;

    // 设置方向
    Motor_SetDirc(Motor, value >= 0 ? DIRC_FOWARD : DIRC_BACKWARD);

    // 计算PWM值（假设定时器周期对应100%占空比）
    uint32_t duty = (uint32_t)fabs(value);
    DL_TimerG_setCaptureCompareValue(Motor->Motor_PWM_TIMX, duty, Motor->Motor_PWM_CH);

    return true;
}

/**
 * @brief 获取电机速度并更新到PID
 */
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    _iq Interval_Time = _IQ(time / 1000.0f);  // 转换为秒
    _iq Encoder_Value = _IQ(*Motor->Motor_Encoder_Addr);
    *Motor->Motor_Encoder_Addr = 0;  // 清空编码器值

    // 计算速度（脉冲/秒）
    _iq Speed = _IQdiv(Encoder_Value, Interval_Time);

    // 根据方向调整速度符号
    if (Motor->Motor_Dirc == DIRC_BACKWARD)
    {
        Speed = -Speed;
    }

    Motor->Motor_PID_Instance.Acutal_Now = Speed;
    return true;
}

/**
 * @brief 设置指定四路电机占空比
 */
void Motor_SetDuty_FourChannel(uint8_t motor_id, float value)
{
    if (motor_id >= 4) return;

    // 限制占空比范围
    if (value > 100.0f) value = 100.0f;
    if (value < -100.0f) value = -100.0f;

    // 设置方向
    Motor_SetDirc_FourChannel(motor_id, value >= 0 ? DIRC_FOWARD : DIRC_BACKWARD);

    // 计算PWM值
    uint32_t duty = (uint32_t)fabs(value);
    DL_TimerG_setCaptureCompareValue(Motor_Array[motor_id].Motor_PWM_TIMX, duty, Motor_Array[motor_id].Motor_PWM_CH);
}

/**
 * @brief 设置所有四路电机占空比
 */
void Motor_SetAllDuty_FourChannel(float m0, float m1, float m2, float m3)
{
    Motor_SetDuty_FourChannel(0, m0);
    Motor_SetDuty_FourChannel(1, m1);
    Motor_SetDuty_FourChannel(2, m2);
    Motor_SetDuty_FourChannel(3, m3);
}

/**
 * @brief 停止指定四路电机
 */
void Motor_Stop_FourChannel(uint8_t motor_id)
{
    if (motor_id >= 4) return;
    Motor_SetDuty_FourChannel(motor_id, 0.0f);
}

/**
 * @brief 停止所有四路电机
 */
void Motor_StopAll_FourChannel(void)
{
    for(uint8_t i = 0; i < 4; i++)
    {
        Motor_SetDuty_FourChannel(i, 0.0f);
    }
}