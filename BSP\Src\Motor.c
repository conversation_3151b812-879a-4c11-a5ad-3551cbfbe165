#include "Motor.h"

// TB6612 四路驱动方向控制宏定义
#define AIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN1_PIN)
#define AIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN1_PIN)
#define AIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN2_PIN)
#define AIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN2_PIN)

#define BIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN1_PIN)
#define BIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN1_PIN)
#define BIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN2_PIN)
#define BIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN2_PIN)

#define CIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN1_PIN)
#define CIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN1_PIN)
#define CIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN2_PIN)
#define CIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_CIN2_PIN)

#define DIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN1_PIN)
#define DIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN1_PIN)
#define DIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN2_PIN)
#define DIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_DIN2_PIN)

// 四路电机实例定义
MOTOR_Def_t Motor_1 = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[0],
    .Motor_PWM_TIMX = Motor_PWM_INST,
    .Motor_PWM_CH = DL_TIMER_CC_0_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_AIN1_PIN,
    .Motor_IN2_PIN = DIRC_CTRL_AIN2_PIN,
    .Motor_ID = 1
};

MOTOR_Def_t Motor_2 = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[1],
    .Motor_PWM_TIMX = Motor_PWM_INST,
    .Motor_PWM_CH = DL_TIMER_CC_1_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_BIN1_PIN,
    .Motor_IN2_PIN = DIRC_CTRL_BIN2_PIN,
    .Motor_ID = 2
};

MOTOR_Def_t Motor_3 = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[2],
    .Motor_PWM_TIMX = Motor_PWM2_INST,
    .Motor_PWM_CH = DL_TIMER_CC_0_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_CIN1_PIN,
    .Motor_IN2_PIN = DIRC_CTRL_CIN2_PIN,
    .Motor_ID = 3
};

MOTOR_Def_t Motor_4 = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[3],
    .Motor_PWM_TIMX = Motor_PWM2_INST,
    .Motor_PWM_CH = DL_TIMER_CC_1_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_DIN1_PIN,
    .Motor_IN2_PIN = DIRC_CTRL_DIN2_PIN,
    .Motor_ID = 4
};

/**
 * @brief 开启电机（兼容性函数，启动前两个电机）
 */
void Motor_Start(void)
{
    // 开启PWM定时器
    DL_TimerG_startCounter(Motor_PWM_INST);
    DL_TimerG_startCounter(Motor_PWM2_INST);

    // 初始占空比设为0
    Motor_SetDuty(&Motor_1, 0.0f);
    Motor_SetDuty(&Motor_2, 0.0f);

    // 初始化PID
    PID_IQ_Init(&Motor_1.Motor_PID_Instance);
    PID_IQ_Init(&Motor_2.Motor_PID_Instance);

    // 设置PID参数
    PID_IQ_SetParams(&Motor_1.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_2.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
}

/**
 * @brief 启动所有四路电机
 */
void Motor_StartAll(void)
{
    // 开启PWM定时器
    DL_TimerG_startCounter(Motor_PWM_INST);
    DL_TimerG_startCounter(Motor_PWM2_INST);

    // 初始占空比设为0
    Motor_SetDuty(&Motor_1, 0.0f);
    Motor_SetDuty(&Motor_2, 0.0f);
    Motor_SetDuty(&Motor_3, 0.0f);
    Motor_SetDuty(&Motor_4, 0.0f);

    // 初始化PID
    PID_IQ_Init(&Motor_1.Motor_PID_Instance);
    PID_IQ_Init(&Motor_2.Motor_PID_Instance);
    PID_IQ_Init(&Motor_3.Motor_PID_Instance);
    PID_IQ_Init(&Motor_4.Motor_PID_Instance);

    // 设置PID参数
    PID_IQ_SetParams(&Motor_1.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_2.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_3.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_4.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
}

/**
 * @brief 设置电机正反转（适配TB6612四路驱动）
 */
static bool Motor_SetDirc(MOTOR_Def_t *Motor, Motor_DIRC_Def_t Dirc)
{
    if (Motor == NULL) return false;

    if (Dirc == DIRC_FOWARD)
    {
        switch (Motor->Motor_ID)
        {
            case 1: // 电机1：AIN1=H, AIN2=L
                AIN1_SET();
                AIN2_CLR();
                break;
            case 2: // 电机2：BIN1=H, BIN2=L
                BIN1_SET();
                BIN2_CLR();
                break;
            case 3: // 电机3：CIN1=H, CIN2=L
                CIN1_SET();
                CIN2_CLR();
                break;
            case 4: // 电机4：DIN1=H, DIN2=L
                DIN1_SET();
                DIN2_CLR();
                break;
            default:
                return false;
        }
        Motor->Motor_Dirc = DIRC_FOWARD;
        return true;
    }
    else if (Dirc == DIRC_BACKWARD)
    {
        switch (Motor->Motor_ID)
        {
            case 1: // 电机1：AIN1=L, AIN2=H
                AIN1_CLR();
                AIN2_SET();
                break;
            case 2: // 电机2：BIN1=L, BIN2=H
                BIN1_CLR();
                BIN2_SET();
                break;
            case 3: // 电机3：CIN1=L, CIN2=H
                CIN1_CLR();
                CIN2_SET();
                break;
            case 4: // 电机4：DIN1=L, DIN2=H
                DIN1_CLR();
                DIN2_SET();
                break;
            default:
                return false;
        }
        Motor->Motor_Dirc = DIRC_BACKWARD;
        return true;
    }
    return false;
}

/**
 * @brief 设置电机占空比（-100~100）
 */
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value)
{
    if (Motor == NULL) return false;

    // 限制占空比范围
    if (value > 100.0f) value = 100.0f;
    if (value < -100.0f) value = -100.0f;

    // 设置方向
    Motor_SetDirc(Motor, value >= 0 ? DIRC_FOWARD : DIRC_BACKWARD);

    // 计算PWM值（假设定时器周期对应100%占空比）
    uint32_t duty = (uint32_t)fabs(value);
    DL_TimerG_setCaptureCompareValue(Motor->Motor_PWM_TIMX, duty, Motor->Motor_PWM_CH);

    return true;
}

/**
 * @brief 获取电机速度并更新到PID
 */
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    _iq Interval_Time = _IQ(time / 1000.0f);  // 转换为秒
    _iq Encoder_Value = _IQ(*Motor->Motor_Encoder_Addr);
    *Motor->Motor_Encoder_Addr = 0;  // 清空编码器值

    // 计算速度（脉冲/秒）
    _iq Speed = _IQdiv(Encoder_Value, Interval_Time);

    // 根据方向调整速度符号
    if (Motor->Motor_Dirc == DIRC_BACKWARD)
    {
        Speed = -Speed;
    }

    Motor->Motor_PID_Instance.Acutal_Now = Speed;
    return true;
}

/**
 * @brief 设置所有电机占空比
 */
void Motor_SetAllDuty(float motor1, float motor2, float motor3, float motor4)
{
    Motor_SetDuty(&Motor_1, motor1);
    Motor_SetDuty(&Motor_2, motor2);
    Motor_SetDuty(&Motor_3, motor3);
    Motor_SetDuty(&Motor_4, motor4);
}

/**
 * @brief 停止所有电机
 */
void Motor_StopAll(void)
{
    Motor_SetDuty(&Motor_1, 0.0f);
    Motor_SetDuty(&Motor_2, 0.0f);
    Motor_SetDuty(&Motor_3, 0.0f);
    Motor_SetDuty(&Motor_4, 0.0f);
}